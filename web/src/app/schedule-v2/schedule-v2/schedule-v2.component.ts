import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ReactiveFormsModule,
  FormBuilder,
  FormGroup,
  Validators,
} from '@angular/forms';
import { DentistService } from '../../core/services/dentist.service';
import { SchedulingService } from '../../core/services/scheduling.service';
import { PatientService } from '../../core/services/patient.service';
import { TreatmentPlanService } from '../../core/services/treatment-plan.service';
import { SidebarService } from '../../core/services/sidebar.service';
import { NotificationService } from '../../core/services/notification.service';
import { AppointmentCategoriesService } from '../../appointment-categories/services/appointment-categories.service';
import { ProcedureService } from '../../core/services/procedure.service';
import { Dentist } from '../../core/models/dentist.model';
import { Patient } from '../../core/models/patient.model';
import { Scheduling } from '../../core/models/scheduling.model';
import { TreatmentPlan } from '../../core/models/treatment-plan.model';
import { TreatmentProcedure } from '../../core/models/treatment-procedure.model';
import { AppointmentCategory } from '../../appointment-categories/models/appointment-category.model';
import { Procedure } from '../../core/models/procedure.model';
import { AppointmentObservationsHistoryComponent } from '../../shared/components/appointment-observations-history/appointment-observations-history.component';
import { SharedModule } from '../../shared/shared.module';
import { AppointmentFormComponent } from '../../shared/components/appointment-form/appointment-form.component';
import { Subscription } from 'rxjs';

interface TimeSlot {
  time: string;
  hour: number;
  minute: number;
}

interface ScheduleAppointment {
  id: number;
  patientId: number;
  patientName: string;
  patientType: string;
  status: string;
  startTime: string;
  endTime: string;
  dentistId: number;
  duration: number; // em minutos
  date: Date;
  notes?: string;
  treatmentPlanId?: number;
  appointmentCategory?: any; // Categoria do agendamento
  // Campos adicionais para o formulário
  email?: string;
  phone?: string;
  isFirstAppointment?: boolean;
  scheduledBy?: string;
  procedureIds?: number[];
}

interface WeekDay {
  date: Date;
  dayName: string;
  dayNumber: number;
  isToday: boolean;
  isWeekend: boolean;
}

interface MonthDay {
  date: Date;
  dayNumber: number;
  isToday: boolean;
  isCurrentMonth: boolean;
  isWeekend: boolean;
  appointments: ScheduleAppointment[];
}

type ViewMode = 'day' | 'week' | 'month';

interface PatientType {
  name: string;
  color: string;
  bgColor: string;
}

interface AppointmentStatus {
  name: string;
  color: string;
  bgColor: string;
}

@Component({
  selector: 'app-schedule-v2',
  templateUrl: './schedule-v2.component.html',
  styleUrls: ['./schedule-v2.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    AppointmentObservationsHistoryComponent,
    SharedModule,
    AppointmentFormComponent,
  ],
})
export class ScheduleV2Component implements OnInit, OnDestroy {
  dentists: Dentist[] = [];
  selectedDentists: { [key: number]: boolean } = {};
  isLoading = true;
  isSheetOpen = false;
  isSheetAnimating = false;
  private readonly STORAGE_KEY = 'schedule-v2-selected-dentists';

  // Controle do sidebar principal e filtro dropdown
  isSidebarCollapsed = false;
  isFilterDropdownOpen = false;
  private sidebarSubscription: Subscription = new Subscription();

  // Controle de responsividade
  isMobile = false;
  isTablet = false;
  isDesktop = false;

  // Controle do drag scroll
  private gridContainer: HTMLElement | null = null;

  // Dados da agenda
  timeSlots: TimeSlot[] = [];
  appointments: ScheduleAppointment[] = [];
  currentDate = new Date();
  schedulings: Scheduling[] = [];

  // Controle de visualização
  viewMode: ViewMode = 'day';
  weekDays: WeekDay[] = [];
  currentWeekStart = new Date();

  // Dados para visualização mensal
  monthDays: MonthDay[] = [];
  currentMonth = new Date();
  monthNames = [
    'Janeiro',
    'Fevereiro',
    'Março',
    'Abril',
    'Maio',
    'Junho',
    'Julho',
    'Agosto',
    'Setembro',
    'Outubro',
    'Novembro',
    'Dezembro',
  ];
  dayNames = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

  // Configurações de tipos e status
  patientTypes: { [key: string]: PatientType } = {
    Particular: {
      name: 'Particular',
      color: 'text-blue-700',
      bgColor: 'bg-blue-100',
    },
    Convênio: {
      name: 'Convênio',
      color: 'text-green-700',
      bgColor: 'bg-green-100',
    },
    SUS: { name: 'SUS', color: 'text-purple-700', bgColor: 'bg-purple-100' },
    Ouro: { name: 'Ouro', color: 'text-yellow-700', bgColor: 'bg-yellow-100' },
    Safira: { name: 'Safira', color: 'text-blue-700', bgColor: 'bg-blue-100' },
    Diamante: {
      name: 'Diamante',
      color: 'text-purple-700',
      bgColor: 'bg-purple-100',
    },
    default: {
      name: 'Não definido',
      color: 'text-gray-700',
      bgColor: 'bg-gray-100',
    },
  };

  appointmentStatuses: { [key: string]: AppointmentStatus } = {
    confirmed: {
      name: 'Confirmado',
      color: 'text-green-700',
      bgColor: 'bg-green-50',
    },
    unconfirmed: {
      name: 'Não confirmado',
      color: 'text-yellow-700',
      bgColor: 'bg-yellow-50',
    },
    late: {
      name: 'Atrasado',
      color: 'text-orange-700',
      bgColor: 'bg-orange-50',
    },
    'no-show': {
      name: 'Não compareceu',
      color: 'text-red-700',
      bgColor: 'bg-red-50',
    },
    cancelled: {
      name: 'Desmarcado',
      color: 'text-gray-700',
      bgColor: 'bg-gray-50',
    },
    rescheduled: {
      name: 'Remarcado',
      color: 'text-blue-700',
      bgColor: 'bg-blue-50',
    },
    'in-progress': {
      name: 'Em andamento',
      color: 'text-indigo-700',
      bgColor: 'bg-indigo-50',
    },
    completed: {
      name: 'Concluído',
      color: 'text-emerald-700',
      bgColor: 'bg-emerald-50',
    },
    // Para compatibilidade com registros antigos
    'scheduled-confirmed': {
      name: 'Confirmado',
      color: 'text-green-700',
      bgColor: 'bg-green-50',
    },
    'scheduled-unconfirmed': {
      name: 'Não confirmado',
      color: 'text-yellow-700',
      bgColor: 'bg-yellow-50',
    },
    unscheduled: {
      name: 'Não agendado',
      color: 'text-orange-700',
      bgColor: 'bg-orange-50',
    },
  };

  // Formulário de agendamento
  appointmentForm: FormGroup;
  patients: Patient[] = [];
  filteredPatients: Patient[] = [];
  patientSearchTerm = '';
  showPatientDropdown = false;
  isSubmittingAppointment = false;

  // Dados do paciente selecionado para exibição
  selectedPatientEmail = '';
  selectedPatientPhone = '';

  // Procedimentos e Planos de Tratamento
  treatmentPlans: TreatmentPlan[] = [];
  filteredTreatmentPlans: TreatmentPlan[] = [];
  selectedTreatmentPlan: TreatmentPlan | null = null;
  availableProcedures: TreatmentProcedure[] = [];
  selectedProcedures: TreatmentProcedure[] = [];
  calculatedDuration = 30; // Duração padrão em minutos
  calculatedEndTime = '';

  // Controle do agendamento selecionado para edição
  selectedAppointment: ScheduleAppointment | null = null;
  isEditingAppointment = false;
  selectedAppointmentProcedures: TreatmentProcedure[] = [];
  selectedAppointmentTreatmentPlan: TreatmentPlan | null = null;

  // Controle do formulário no sheet
  isAppointmentFormValid = false;

  // Controle do histórico de observações
  isObservationsHistoryOpen = false;
  selectedPatientIdForHistory: number = 0;

  // Categorias de agendamento e procedimentos
  appointmentCategories: AppointmentCategory[] = [];
  allProcedures: Procedure[] = [];
  selectedProcedureIds: number[] = [];
  selectedProceduresForAppointment: Procedure[] = [];

  constructor(
    private dentistService: DentistService,
    private schedulingService: SchedulingService,
    private patientService: PatientService,
    private treatmentPlanService: TreatmentPlanService,
    private sidebarService: SidebarService,
    private notificationService: NotificationService,
    private appointmentCategoriesService: AppointmentCategoriesService,
    private procedureService: ProcedureService,
    private fb: FormBuilder
  ) {
    // Inicializar formulário de agendamento
    this.appointmentForm = this.fb.group({
      patientId: ['', Validators.required],
      dentistId: [''], // Opcional como no scheduling-form
      date: ['', Validators.required],
      time: ['', Validators.required],
      status: ['scheduled-unconfirmed', Validators.required],
      notes: [''],
      email: [''], // Email editável
      phone: [''], // Celular editável
      isFirstAppointment: [false], // É primeira consulta
      appointmentCategoryId: [null], // Categoria do agendamento
      procedureIds: [[]], // IDs dos procedimentos selecionados
    });
  }

  ngOnInit(): void {
    // Colapsar o sidebar por padrão ao acessar schedule-v2
    this.sidebarService.collapse();

    // Detectar tamanho da tela
    this.checkScreenSize();

    this.loadDentists();
    this.loadPatients();
    this.loadAppointmentCategories();
    this.loadAllProcedures();
    this.generateTimeSlots();
    this.generateWeekDays();
    this.generateMonthDays();
    // loadSchedulings() será chamado após carregar dentistas

    // Inscrever-se no estado do sidebar
    this.sidebarSubscription.add(
      this.sidebarService.isCollapsed$.subscribe((isCollapsed) => {
        this.isSidebarCollapsed = isCollapsed;
        // Fechar o dropdown de filtros quando o sidebar mudar de estado
        if (this.isFilterDropdownOpen) {
          this.isFilterDropdownOpen = false;
        }
      })
    );

    // Fechar dropdown de filtros quando clicar fora
    document.addEventListener('click', this.onDocumentClick.bind(this));

    // Listener para mudanças no tamanho da tela
    window.addEventListener('resize', this.onWindowResize.bind(this));

    // Inicializar drag scroll após o DOM estar pronto
    setTimeout(() => {
      this.initDragScroll();
    }, 100); // Reduzir delay para melhor responsividade
  }

  ngOnDestroy(): void {
    this.sidebarSubscription.unsubscribe();
    document.removeEventListener('click', this.onDocumentClick.bind(this));
    window.removeEventListener('resize', this.onWindowResize.bind(this));
    this.cleanupDragScrollListeners();
  }

  loadDentists(): void {
    this.dentistService.getAllDentists().subscribe({
      next: (dentists) => {
        this.dentists = dentists.filter((d) => d.active);
        this.initializeSelectedDentists();
        this.isLoading = false;

        // Carregar agendamentos após carregar dentistas
        console.log('Dentistas carregados, carregando agendamentos...');
        this.loadSchedulings();
      },
      error: (error) => {
        console.error('Erro ao carregar dentistas:', error);
        this.isLoading = false;
      },
    });
  }

  loadPatients(): void {
    this.patientService.getAllPatients().subscribe({
      next: (patients) => {
        this.patients = patients;
        this.filteredPatients = [...this.patients];
      },
      error: (error) => {
        console.error('Erro ao carregar pacientes:', error);
      },
    });
  }

  loadAppointmentCategories(): void {
    this.appointmentCategoriesService
      .getAllCategories({ isActive: true })
      .subscribe({
        next: (response) => {
          this.appointmentCategories = response.data;
        },
        error: (error) => {
          console.error('Erro ao carregar categorias de agendamento:', error);
        },
      });
  }

  loadAllProcedures(): void {
    this.procedureService.getAllProcedures().subscribe({
      next: (procedures) => {
        this.allProcedures = procedures.filter((p) => p.status === 'ACTIVE');
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
      },
    });
  }

  private initializeSelectedDentists(): void {
    // Tentar carregar seleção do localStorage
    const savedSelection = this.loadSelectedDentistsFromStorage();

    if (savedSelection && Object.keys(savedSelection).length > 0) {
      // Usar exatamente a seleção salva, sem forçar seleção de novos dentistas
      this.dentists.forEach((dentist) => {
        if (savedSelection.hasOwnProperty(dentist.id.toString())) {
          this.selectedDentists[dentist.id] =
            savedSelection[dentist.id.toString()];
        } else {
          // Se o dentista não estava na seleção salva, marcar como NÃO selecionado
          this.selectedDentists[dentist.id] = false;
        }
      });
    } else {
      // Se não há seleção salva, selecionar todos os dentistas por padrão
      this.dentists.forEach((dentist) => {
        this.selectedDentists[dentist.id] = true;
      });
      console.log(
        'Nenhuma seleção salva encontrada. Selecionando todos os dentistas por padrão.'
      );
    }

    // Salvar a seleção inicial no localStorage
    this.saveSelectedDentistsToStorage();
  }

  onDentistToggle(dentistId: number): void {
    this.selectedDentists[dentistId] = !this.selectedDentists[dentistId];
    // Salvar no localStorage sempre que houver mudança
    this.saveSelectedDentistsToStorage();
    // Recarregar agendamentos quando a seleção de dentistas mudar
    this.loadSchedulings();
    console.log('Dentistas selecionados:', this.getSelectedDentists());
  }

  getSelectedDentists(): number[] {
    return Object.keys(this.selectedDentists)
      .map((id) => parseInt(id))
      .filter((id) => this.selectedDentists[id]);
  }

  openNewAppointmentSheet(): void {
    this.isSheetAnimating = true;

    // Garantir que não está em modo de edição
    this.isEditingAppointment = false;
    this.selectedAppointment = null;
    this.selectedAppointmentProcedures = [];
    this.selectedAppointmentTreatmentPlan = null;

    // Resetar formulário e configurar valores padrão
    this.resetAppointmentForm();

    // Pequeno delay para permitir que o DOM seja atualizado antes da animação
    setTimeout(() => {
      this.isSheetOpen = true;
    }, 10);
  }

  closeSheet(): void {
    this.isSheetAnimating = true;
    this.isSheetOpen = false;
    this.showPatientDropdown = false;
    this.patientSearchTerm = '';

    // Limpar dados de edição
    this.isEditingAppointment = false;
    this.selectedAppointment = null;
    this.selectedAppointmentProcedures = [];
    this.selectedAppointmentTreatmentPlan = null;

    // Aguardar a animação terminar antes de remover o elemento do DOM
    setTimeout(() => {
      this.isSheetAnimating = false;
    }, 300);
  }

  // Método para abrir agendamento em modo de edição
  openAppointmentForEdit(appointment: ScheduleAppointment): void {
    // Configurar modo de edição
    this.isEditingAppointment = true;
    this.isSheetAnimating = true;
    this.selectedAppointmentProcedures = [];
    this.selectedAppointmentTreatmentPlan = null;

    console.log('=== ABRINDO AGENDAMENTO PARA EDIÇÃO ===');
    console.log('ID do agendamento:', appointment.id);
    console.log('PatientId do agendamento:', appointment.patientId);
    console.log('Dados completos do appointment:', appointment);

    // Definir selectedAppointment imediatamente com os dados básicos
    this.selectedAppointment = {
      ...appointment,
      status: appointment.status || 'unconfirmed',
      duration: appointment.duration || 30,
      endTime:
        appointment.endTime ||
        this.calculateEndTime(
          appointment.startTime,
          appointment.duration || 30
        ),
    };

    console.log(
      '📝 SelectedAppointment inicial definido:',
      this.selectedAppointment
    );

    // Pequeno delay para permitir que o DOM seja atualizado antes da animação
    setTimeout(() => {
      this.isSheetOpen = true;
    }, 10);

    // Buscar dados atualizados do agendamento
    this.schedulingService.getSchedulingById(appointment.id).subscribe({
      next: (detailedScheduling) => {
        console.log('✅ Agendamento carregado:', detailedScheduling);
        console.log(
          '🔍 DentistId no detailedScheduling:',
          detailedScheduling.dentistId
        );

        // Converter para o formato ScheduleAppointment com todos os dados
        const fullAppointment =
          this.convertSchedulingToAppointment(detailedScheduling);

        // Definir o selectedAppointment com todos os dados
        this.selectedAppointment = {
          ...fullAppointment,
          // Garantir que todos os campos necessários estejam presentes
          status: detailedScheduling.status || 'unconfirmed',
          appointmentCategory: detailedScheduling.appointmentCategory || null,
          duration:
            detailedScheduling.duration ||
            this.estimateDuration(detailedScheduling),
          endTime:
            detailedScheduling.endTime ||
            this.calculateEndTime(
              detailedScheduling.time?.substring(0, 5) || '09:00',
              detailedScheduling.duration || 30
            ),
          // Garantir que os campos importantes estejam presentes
          dentistId: detailedScheduling.dentistId || fullAppointment.dentistId,
          scheduledBy: fullAppointment.scheduledBy,
          email: fullAppointment.email,
          phone: fullAppointment.phone,
          isFirstAppointment: fullAppointment.isFirstAppointment,
        };

        console.log(
          '✅ SelectedAppointment definido:',
          this.selectedAppointment
        );
        console.log(
          '🔍 DentistId no selectedAppointment:',
          this.selectedAppointment.dentistId
        );
        console.log(
          '🔍 ScheduledBy no selectedAppointment:',
          this.selectedAppointment.scheduledBy
        );

        // Carregar procedimentos do agendamento primeiro
        this.loadAppointmentProcedures(appointment.id);

        // Carregar plano de tratamento se existir
        if (detailedScheduling.treatmentPlanId) {
          this.loadTreatmentPlan(detailedScheduling.treatmentPlanId);
        }
      },
      error: (error) => {
        console.error('❌ Erro ao carregar agendamento:', error);
        // Em caso de erro, usar os dados básicos do appointment com valores padrão
        this.selectedAppointment = {
          ...appointment,
          status: appointment.status || 'unconfirmed',
          duration: appointment.duration || 30,
          endTime:
            appointment.endTime ||
            this.calculateEndTime(
              appointment.startTime,
              appointment.duration || 30
            ),
        };
      },
    });
  }

  // Método para converter Scheduling da API para ScheduleAppointment
  private convertSchedulingToAppointment(scheduling: any): ScheduleAppointment {
    console.log('🔄 Convertendo scheduling para appointment:', {
      schedulingId: scheduling.id,
      schedulingPatientId: scheduling.patientId,
      patientObject: scheduling.patient,
      patientObjectId: scheduling.patient?.id,
    });

    // Normalizar o horário para formato HH:MM (remover segundos se existir)
    const startTime = scheduling.time.substring(0, 5); // "09:00:00" -> "09:00"
    const duration = scheduling.duration || this.estimateDuration(scheduling);
    const endTime = scheduling.endTime
      ? scheduling.endTime.substring(0, 5)
      : this.calculateEndTime(startTime, duration);

    // Extrair nome do paciente corretamente
    const patientName =
      scheduling.patient?.name ||
      scheduling.patientName ||
      'Paciente não identificado';
    const patientType = this.getPatientTypeFromScheduling(scheduling);

    // Extrair patientId corretamente - priorizar scheduling.patientId se existir
    const patientId = scheduling.patientId || scheduling.patient?.id || 0;

    console.log('🔍 PatientId extraído:', patientId);

    // Corrigir conversão da data - garantir que seja interpretada corretamente
    let appointmentDate: Date;
    if (typeof scheduling.date === 'string') {
      // Se a data vem como string "2024-06-02", criar data local sem problemas de timezone
      const dateParts = scheduling.date.split('-');
      appointmentDate = new Date(
        parseInt(dateParts[0]),
        parseInt(dateParts[1]) - 1,
        parseInt(dateParts[2])
      );
    } else {
      appointmentDate = new Date(scheduling.date);
    }

    const appointment = {
      id: scheduling.id,
      patientId: patientId,
      patientName: patientName,
      patientType: patientType,
      status: scheduling.status,
      startTime: startTime,
      endTime: endTime,
      dentistId: scheduling.dentistId,
      duration: duration,
      date: appointmentDate,
      notes: scheduling.notes,
      treatmentPlanId: scheduling.treatmentPlanId,
      // Adicionar campos adicionais para o formulário
      appointmentCategory: scheduling.appointmentCategory,
      email: (scheduling as any).email || '',
      phone: (scheduling as any).phone || '',
      isFirstAppointment: (scheduling as any).isFirstAppointment || false,
      scheduledBy: (scheduling as any).scheduledBy || '',
      procedureIds: (scheduling as any).procedureIds || [],
      // Adicionar campos do objeto completo do backend
      dentist: scheduling.dentist || null,
      patient: scheduling.patient || null,
      procedures: scheduling.procedures || [],
    };

    console.log('✅ Appointment convertido:', {
      id: appointment.id,
      patientId: appointment.patientId,
      patientName: appointment.patientName,
      dentistId: appointment.dentistId,
      dentist: appointment.dentist,
      procedures: appointment.procedures,
    });

    return appointment;
  }

  // Métodos do formulário de agendamento
  resetAppointmentForm(): void {
    this.appointmentForm.reset({
      status: 'scheduled-unconfirmed',
      email: '',
      phone: '',
      isFirstAppointment: false,
      appointmentCategoryId: null,
      procedureIds: [],
    });
    this.patientSearchTerm = '';
    this.showPatientDropdown = false;

    // Limpar dados do paciente selecionado
    this.selectedPatientEmail = '';
    this.selectedPatientPhone = '';

    // Limpar procedimentos selecionados
    this.selectedProceduresForAppointment = [];

    // Definir duração padrão
    this.calculatedDuration = 30;
    this.calculatedEndTime = '';

    // Definir data padrão como hoje
    const today = new Date();
    const formattedDate = this.formatDateForInput(today);
    this.appointmentForm.patchValue({
      date: formattedDate,
      time: '09:00',
    });

    // Calcular hora de término inicial
    const startTime = this.appointmentForm.get('time')?.value;
    if (startTime) {
      this.calculatedEndTime = this.calculateEndTime(
        startTime,
        this.calculatedDuration
      );
    }
  }

  filterPatients(): void {
    if (!this.patientSearchTerm.trim()) {
      this.filteredPatients = [...this.patients];
      return;
    }

    const searchTerm = this.patientSearchTerm.toLowerCase().trim();
    this.filteredPatients = this.patients.filter(
      (patient) =>
        patient.name.toLowerCase().includes(searchTerm) ||
        (patient.cpf && patient.cpf.includes(searchTerm))
    );
  }

  selectPatient(patient: Patient): void {
    this.patientSearchTerm = patient.name;
    this.appointmentForm.patchValue({
      patientId: patient.id,
      email: patient.email || '', // Preencher email no formulário
      phone: patient.phone || '', // Preencher celular no formulário
    });
    this.showPatientDropdown = false;

    // Armazenar dados do paciente para exibição
    this.selectedPatientEmail = patient.email || '';
    this.selectedPatientPhone = patient.phone || '';
  }

  // Método para carregar dados do paciente durante a edição
  loadPatientDataForEdit(patientId: number): void {
    console.log(
      '🔍 Carregando dados do paciente para edição. PatientId:',
      patientId
    );

    // Verificar se o patientId é válido
    if (!patientId || patientId === 0) {
      console.warn('⚠️ PatientId inválido:', patientId);
      this.selectedPatientEmail = '';
      this.selectedPatientPhone = '';
      return;
    }

    const patient = this.patients.find((p) => p.id === patientId);
    if (patient) {
      console.log('✅ Paciente encontrado na lista local:', patient);
      this.selectedPatientEmail = patient.email || '';
      this.selectedPatientPhone = patient.phone || '';
    } else {
      // Se o paciente não estiver na lista carregada, buscar diretamente
      console.log(
        '🔍 Paciente não encontrado na lista local, buscando na API...'
      );
      this.patientService.getPatient(patientId).subscribe({
        next: (patient) => {
          if (patient) {
            console.log('✅ Paciente carregado da API:', patient);
            this.selectedPatientEmail = patient.email || '';
            this.selectedPatientPhone = patient.phone || '';
          }
        },
        error: (error) => {
          console.error('❌ Erro ao carregar dados do paciente:', error);
          this.selectedPatientEmail = '';
          this.selectedPatientPhone = '';
        },
      });
    }
  }

  // Método para carregar os planos de tratamento de um paciente (igual ao scheduling-form)
  loadPatientTreatmentPlans(patientId: number, selectedPlanId?: number): void {
    this.treatmentPlanService.getTreatmentPlansByPatient(patientId).subscribe({
      next: (plans) => {
        console.log('Planos de tratamento carregados:', plans);

        // Filtrar apenas planos ativos (status 'open')
        this.filteredTreatmentPlans = plans.filter(
          (plan) => plan.status === 'open'
        );

        // Verificar se os planos têm procedimentos
        this.filteredTreatmentPlans.forEach((plan) => {
          if (!plan.procedures || !Array.isArray(plan.procedures)) {
            console.warn(
              `Plano ${plan.id} não tem procedimentos ou não é um array:`,
              plan.procedures
            );
            plan.procedures = [];
          } else {
            console.log(
              `Plano ${plan.id} tem ${plan.procedures.length} procedimentos`
            );
          }
        });

        // Limpar procedimentos disponíveis quando carrega novos planos
        this.availableProcedures = [];
        this.selectedProcedures = [];

        // Se houver um plano selecionado, definir como o plano atual
        if (selectedPlanId) {
          this.selectedTreatmentPlan =
            this.filteredTreatmentPlans.find(
              (plan) => plan.id === Number(selectedPlanId)
            ) || null;
          if (this.selectedTreatmentPlan) {
            this.loadProceduresFromSelectedPlan();
          }
          console.log(
            'Plano de tratamento selecionado:',
            this.selectedTreatmentPlan
          );
        } else {
          this.selectedTreatmentPlan = null;
        }

        console.log(
          'Planos de tratamento filtrados:',
          this.filteredTreatmentPlans
        );
      },
      error: (error) => {
        console.error(
          'Erro ao carregar planos de tratamento do paciente:',
          error
        );
        this.filteredTreatmentPlans = [];
        this.availableProcedures = [];
        this.selectedProcedures = [];
      },
    });
  }

  // Método específico para carregar planos durante a edição de agendamento
  loadPatientTreatmentPlansForEdit(
    patientId: number,
    selectedPlanId?: number
  ): void {
    this.treatmentPlanService.getTreatmentPlansByPatient(patientId).subscribe({
      next: (plans) => {
        console.log('Planos de tratamento carregados para edição:', plans);

        // Filtrar apenas planos ativos (status 'open')
        this.filteredTreatmentPlans = plans.filter(
          (plan) => plan.status === 'open'
        );

        // Verificar se os planos têm procedimentos
        this.filteredTreatmentPlans.forEach((plan) => {
          if (!plan.procedures || !Array.isArray(plan.procedures)) {
            console.warn(
              `Plano ${plan.id} não tem procedimentos ou não é um array:`,
              plan.procedures
            );
            plan.procedures = [];
          } else {
            console.log(
              `Plano ${plan.id} tem ${plan.procedures.length} procedimentos`
            );
          }
        });

        // Se houver um plano selecionado, configurar corretamente
        if (selectedPlanId) {
          this.selectedTreatmentPlan =
            this.filteredTreatmentPlans.find(
              (plan) => plan.id === Number(selectedPlanId)
            ) || null;

          if (this.selectedTreatmentPlan) {
            // Carregar todos os procedimentos disponíveis do plano (não concluídos)
            this.availableProcedures =
              this.selectedTreatmentPlan.procedures.filter(
                (proc) =>
                  proc.status === 'pending' || proc.status === 'in_progress'
              );

            // Manter os procedimentos já selecionados no agendamento
            // Filtrar apenas os procedimentos que estão no plano atual
            this.selectedProcedures = this.selectedAppointmentProcedures.filter(
              (appointmentProc) =>
                this.availableProcedures.some(
                  (availableProc) => availableProc.id === appointmentProc.id
                )
            );

            // Recalcular duração e hora de término baseado nos procedimentos selecionados
            this.calculateDurationAndEndTime();

            console.log(
              'Plano de tratamento selecionado para edição:',
              this.selectedTreatmentPlan
            );
            console.log('Procedimentos disponíveis:', this.availableProcedures);
            console.log(
              'Procedimentos do agendamento:',
              this.selectedAppointmentProcedures
            );
            console.log(
              'Procedimentos selecionados (filtrados):',
              this.selectedProcedures
            );
          }
        } else {
          this.selectedTreatmentPlan = null;
          this.availableProcedures = [];
          this.selectedProcedures = [];
        }

        console.log(
          'Planos de tratamento filtrados para edição:',
          this.filteredTreatmentPlans
        );
      },
      error: (error) => {
        console.error(
          'Erro ao carregar planos de tratamento do paciente para edição:',
          error
        );
        this.filteredTreatmentPlans = [];
        this.availableProcedures = [];
        this.selectedProcedures = [];
      },
    });
  }

  // Método para carregar procedimentos do plano selecionado
  loadProceduresFromSelectedPlan(): void {
    if (!this.selectedTreatmentPlan || !this.selectedTreatmentPlan.procedures) {
      this.availableProcedures = [];
      this.selectedProcedures = [];
      return;
    }

    // Filtrar apenas procedimentos não concluídos (pending, in_progress)
    this.availableProcedures = this.selectedTreatmentPlan.procedures.filter(
      (proc) => proc.status === 'pending' || proc.status === 'in_progress'
    );

    // Limpar seleções anteriores
    this.selectedProcedures = [];
    this.calculateDurationAndEndTime();

    console.log(
      'Procedimentos disponíveis do plano selecionado:',
      this.availableProcedures
    );
  }

  toggleProcedureSelection(procedure: TreatmentProcedure): void {
    const index = this.selectedProcedures.findIndex(
      (p) => p.id === procedure.id
    );

    if (index > -1) {
      // Remover procedimento
      this.selectedProcedures.splice(index, 1);
    } else {
      // Adicionar procedimento
      this.selectedProcedures.push(procedure);
    }

    // Atualizar duração e hora de término
    this.calculateDurationAndEndTime();
  }

  isProcedureSelected(procedure: TreatmentProcedure): boolean {
    return this.selectedProcedures.some((p) => p.id === procedure.id);
  }

  // Métodos para gerenciar seleção de procedimentos individuais
  toggleIndividualProcedure(procedure: Procedure): void {
    const index = this.selectedProcedureIds.indexOf(procedure.id);

    if (index > -1) {
      // Remover procedimento
      this.selectedProcedureIds.splice(index, 1);
    } else {
      // Adicionar procedimento
      this.selectedProcedureIds.push(procedure.id);
    }

    // Atualizar o formulário
    this.appointmentForm.patchValue({
      procedureIds: [...this.selectedProcedureIds],
    });

    // Recalcular duração baseada nos procedimentos selecionados
    this.calculateDurationFromIndividualProcedures();
  }

  isIndividualProcedureSelected(procedure: Procedure): boolean {
    return this.selectedProcedureIds.includes(procedure.id);
  }

  calculateDurationFromIndividualProcedures(): void {
    if (this.selectedProcedureIds.length === 0) {
      this.calculatedDuration = 30; // Duração padrão
    } else {
      // Somar duração de todos os procedimentos selecionados
      this.calculatedDuration = this.selectedProcedureIds.reduce(
        (total, procedureId) => {
          const procedure = this.allProcedures.find(
            (p) => p.id === procedureId
          );
          const duration = procedure?.estimatedDuration || 30;
          return total + duration;
        },
        0
      );
    }

    // Calcular hora de término
    const startTime = this.appointmentForm.get('time')?.value;
    if (startTime) {
      this.calculatedEndTime = this.calculateEndTime(
        startTime,
        this.calculatedDuration
      );
    }
  }

  calculateDurationAndEndTime(): void {
    if (this.selectedProcedures.length === 0) {
      this.calculatedDuration = 30; // Duração padrão
    } else {
      // Somar duração de todos os procedimentos selecionados
      this.calculatedDuration = this.selectedProcedures.reduce(
        (total, procedure) => {
          // Usar duração do procedimento base ou duração padrão de 30 minutos
          const duration = procedure.procedure?.estimatedDuration || 30;
          return total + duration;
        },
        0
      );
    }

    // Calcular hora de término
    const startTime = this.appointmentForm.get('time')?.value;
    if (startTime) {
      this.calculatedEndTime = this.calculateEndTime(
        startTime,
        this.calculatedDuration
      );
    }
  }

  // Método chamado quando a hora de início muda
  onTimeChange(): void {
    this.calculateDurationAndEndTime();
  }

  // Método para atualizar o plano de tratamento selecionado
  onTreatmentPlanChange(event: any): void {
    const treatmentPlanId = event.target.value;

    if (!treatmentPlanId) {
      this.selectedTreatmentPlan = null;
      this.appointmentForm.patchValue({ treatmentPlanId: null });
      // Limpar procedimentos quando nenhum plano está selecionado
      this.availableProcedures = [];
      this.selectedProcedures = [];
      this.calculateDurationAndEndTime();
      return;
    }

    // Encontrar o plano de tratamento selecionado
    this.selectedTreatmentPlan =
      this.filteredTreatmentPlans.find(
        (plan) => plan.id === Number(treatmentPlanId)
      ) || null;

    this.appointmentForm.patchValue({
      treatmentPlanId: Number(treatmentPlanId),
    });

    // Carregar procedimentos do plano selecionado
    this.loadProceduresFromSelectedPlan();

    console.log('Plano de tratamento selecionado:', this.selectedTreatmentPlan);
  }

  hidePatientDropdown(): void {
    setTimeout(() => {
      this.showPatientDropdown = false;
    }, 200);
  }

  onProceduresChange(procedures: Procedure[]): void {
    this.selectedProceduresForAppointment = procedures;
    const procedureIds = procedures.map((p) => p.id);

    // Atualizar o formulário
    this.appointmentForm.patchValue({
      procedureIds: procedureIds,
    });

    // Recalcular duração baseada nos procedimentos selecionados
    this.calculateDurationFromSelectedProcedures();
  }

  calculateDurationFromSelectedProcedures(): void {
    if (this.selectedProceduresForAppointment.length === 0) {
      this.calculatedDuration = 30; // Duração padrão
    } else {
      // Somar duração de todos os procedimentos selecionados
      this.calculatedDuration = this.selectedProceduresForAppointment.reduce(
        (total, procedure) => {
          return total + (procedure.estimatedDuration || 30);
        },
        0
      );
    }

    // Calcular hora de término
    const startTime = this.appointmentForm.get('time')?.value;
    if (startTime) {
      this.calculatedEndTime = this.calculateEndTime(
        startTime,
        this.calculatedDuration
      );
    }
  }

  onSubmitAppointment(): void {
    if (this.appointmentForm.invalid) {
      // Marcar todos os campos como touched para mostrar os erros
      Object.keys(this.appointmentForm.controls).forEach((key) => {
        const control = this.appointmentForm.get(key);
        control?.markAsTouched();
      });
      return;
    }

    this.isSubmittingAppointment = true;
    const formValues = this.appointmentForm.value;

    // Preparar os dados para envio
    const schedulingData = {
      ...formValues,
      // Converter campos para número
      patientId: formValues.patientId
        ? Number(formValues.patientId)
        : undefined,
      dentistId:
        formValues.dentistId && formValues.dentistId !== ''
          ? Number(formValues.dentistId)
          : null,
      appointmentCategoryId:
        formValues.appointmentCategoryId &&
        formValues.appointmentCategoryId !== ''
          ? Number(formValues.appointmentCategoryId)
          : null,
      // Procedimentos selecionados
      procedureIds: formValues.procedureIds || [],
      // Duração calculada baseada nos procedimentos
      duration: this.calculatedDuration,
      endTime: this.calculatedEndTime,
      // Novos campos
      email: formValues.email || '',
      phone: formValues.phone || '',
      isFirstAppointment: formValues.isFirstAppointment || false,
    };

    // Para atualização, remover o patientId para não permitir alteração do paciente
    if (this.isEditingAppointment && this.selectedAppointment) {
      delete schedulingData.patientId;
    }

    // Verificar se está editando ou criando
    const isEditing = this.isEditingAppointment && this.selectedAppointment;

    if (isEditing) {
      console.log('=== EDITANDO AGENDAMENTO ===');
      console.log('ID do agendamento:', this.selectedAppointment!.id);
      console.log('Dados do formulário:', formValues);
      console.log('Procedimentos selecionados:', this.selectedProcedures);
      console.log('Duração calculada:', this.calculatedDuration, 'min');
      console.log('Término previsto:', this.calculatedEndTime);
      console.log('Dados para envio:', schedulingData);

      // Atualizar agendamento
      this.schedulingService
        .updateScheduling(this.selectedAppointment!.id, schedulingData)
        .subscribe({
          next: (response) => {
            console.log('Agendamento atualizado com sucesso:', response);
            this.isSubmittingAppointment = false;
            this.closeSheet();
            console.log('Recarregando agendamentos...');
            this.loadSchedulings(); // Recarregar agendamentos
          },
          error: (error) => {
            console.error('Erro ao atualizar agendamento:', error);
            this.isSubmittingAppointment = false;
          },
        });
    } else {
      console.log('=== CRIANDO AGENDAMENTO ===');
      console.log('Dados do formulário:', formValues);
      console.log('Procedimentos selecionados:', this.selectedProcedures);
      console.log('Duração calculada:', this.calculatedDuration, 'min');
      console.log('Término previsto:', this.calculatedEndTime);
      console.log('Dados para envio:', schedulingData);

      // Criar agendamento
      this.schedulingService.createScheduling(schedulingData).subscribe({
        next: (response) => {
          console.log('Agendamento criado com sucesso:', response);
          this.isSubmittingAppointment = false;
          this.closeSheet();
          console.log('Recarregando agendamentos...');
          this.loadSchedulings(); // Recarregar agendamentos
        },
        error: (error) => {
          console.error('Erro ao criar agendamento:', error);
          this.isSubmittingAppointment = false;
        },
      });
    }
  }

  // Método para formatar a data para o input type="date" (YYYY-MM-DD)
  formatDateForInput(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  // Getters para facilitar o acesso aos controles do formulário no template
  get patientIdControl() {
    return this.appointmentForm.get('patientId');
  }

  get dentistIdControl() {
    return this.appointmentForm.get('dentistId');
  }

  get dateControl() {
    return this.appointmentForm.get('date');
  }

  get timeControl() {
    return this.appointmentForm.get('time');
  }

  get isFormValid(): boolean {
    return this.appointmentForm.valid;
  }

  // Inicializar o arraste horizontal para scroll da grid
  initDragScroll(): void {
    // Tentar encontrar o container da grid ativa
    this.gridContainer = document.querySelector(
      '.schedule-grid .h-full.overflow-auto'
    ) as HTMLElement;
    if (!this.gridContainer) return;

    let isMouseDown = false;
    let startX: number;
    let scrollLeft: number;
    let isDragging = false;

    // Mouse events com otimizações de performance
    this.gridContainer.addEventListener('mousedown', (e) => {
      // Ignorar se o clique foi em um card de agendamento
      const target = e.target as HTMLElement;
      if (target.closest('.appointment-card')) {
        return;
      }

      isMouseDown = true;
      isDragging = false;
      this.gridContainer!.classList.add('active');
      startX = e.clientX; // Usar clientX ao invés de pageX para melhor performance
      scrollLeft = this.gridContainer!.scrollLeft;
      e.preventDefault();
      e.stopPropagation();
    });

    document.addEventListener('mouseup', () => {
      isMouseDown = false;
      isDragging = false;
      if (this.gridContainer) {
        this.gridContainer.classList.remove('active');
      }
    });

    document.addEventListener('mousemove', (e) => {
      if (!isMouseDown || !this.gridContainer) return;

      isDragging = true;
      e.preventDefault();
      e.stopPropagation();

      const x = e.clientX;
      const walk = (x - startX) * 1.5; // Reduzir multiplicador para mais controle

      // Usar requestAnimationFrame para scroll mais suave
      requestAnimationFrame(() => {
        if (this.gridContainer) {
          this.gridContainer.scrollLeft = scrollLeft - walk;
        }
      });
    });

    // Touch events otimizados para dispositivos móveis
    this.gridContainer.addEventListener(
      'touchstart',
      (e) => {
        // Ignorar se o toque foi em um card de agendamento
        const target = e.target as HTMLElement;
        if (target.closest('.appointment-card')) {
          return;
        }

        isMouseDown = true;
        isDragging = false;
        this.gridContainer!.classList.add('active');
        startX = e.touches[0].clientX;
        scrollLeft = this.gridContainer!.scrollLeft;
        e.preventDefault();
      },
      { passive: false }
    );

    document.addEventListener('touchend', () => {
      isMouseDown = false;
      isDragging = false;
      if (this.gridContainer) {
        this.gridContainer.classList.remove('active');
      }
    });

    document.addEventListener(
      'touchmove',
      (e) => {
        if (!isMouseDown || !this.gridContainer) return;

        isDragging = true;
        e.preventDefault();

        const x = e.touches[0].clientX;
        const walk = (x - startX) * 1.5;

        // Usar requestAnimationFrame para scroll mais suave
        requestAnimationFrame(() => {
          if (this.gridContainer) {
            this.gridContainer.scrollLeft = scrollLeft - walk;
          }
        });
      },
      { passive: false }
    );

    // Prevenir cliques acidentais após drag
    this.gridContainer.addEventListener(
      'click',
      (e) => {
        if (isDragging) {
          e.preventDefault();
          e.stopPropagation();
          isDragging = false;
        }
      },
      true
    );
  }

  // Limpar os event listeners quando o componente for destruído
  cleanupDragScrollListeners(): void {
    // Os event listeners são adicionados ao document, então não precisamos removê-los
    // individualmente pois eles serão limpos automaticamente quando o componente for destruído
    if (this.gridContainer) {
      this.gridContainer.classList.remove('active');
      this.gridContainer = null;
    }
  }

  selectAllDentists(): void {
    this.dentists.forEach((dentist) => {
      this.selectedDentists[dentist.id] = true;
    });
    // Salvar no localStorage
    this.saveSelectedDentistsToStorage();
    this.loadSchedulings();
    console.log('Todos os dentistas selecionados:', this.getSelectedDentists());
  }

  deselectAllDentists(): void {
    this.dentists.forEach((dentist) => {
      this.selectedDentists[dentist.id] = false;
    });
    // Salvar no localStorage
    this.saveSelectedDentistsToStorage();
    this.loadSchedulings();
    console.log('Todos os dentistas desmarcados:', this.getSelectedDentists());
  }

  areAllDentistsSelected(): boolean {
    return (
      this.dentists.length > 0 &&
      this.dentists.every((dentist) => this.selectedDentists[dentist.id])
    );
  }

  areNoDentistsSelected(): boolean {
    return (
      this.dentists.length > 0 &&
      this.dentists.every((dentist) => !this.selectedDentists[dentist.id])
    );
  }

  generateTimeSlots(): void {
    this.timeSlots = [];
    for (let hour = 8; hour < 19; hour++) {
      for (let minute = 0; minute < 60; minute += 5) {
        const timeString = `${hour.toString().padStart(2, '0')}:${minute
          .toString()
          .padStart(2, '0')}`;
        this.timeSlots.push({
          time: timeString,
          hour,
          minute,
        });
      }
    }
    console.log('TimeSlots gerados:', this.timeSlots.length, 'slots');
  }

  loadSchedulings(): void {
    let startDate: Date;
    let endDate: Date;

    if (this.viewMode === 'week') {
      // Para visualização semanal, buscar agendamentos da semana inteira
      startDate = new Date(this.currentWeekStart);
      endDate = new Date(this.currentWeekStart);
      endDate.setDate(endDate.getDate() + 6); // Adicionar 6 dias para completar a semana
    } else if (this.viewMode === 'month') {
      // Para visualização mensal, buscar agendamentos do mês inteiro
      startDate = new Date(
        this.currentMonth.getFullYear(),
        this.currentMonth.getMonth(),
        1
      );
      endDate = new Date(
        this.currentMonth.getFullYear(),
        this.currentMonth.getMonth() + 1,
        0
      );
    } else {
      // Para visualização diária, buscar apenas do dia atual
      startDate = new Date(this.currentDate);
      endDate = new Date(this.currentDate);
    }

    console.log('=== DEBUG CARREGAMENTO AGENDAMENTOS ===');
    console.log('Modo de visualização:', this.viewMode);
    console.log('Data atual:', this.currentDate);
    console.log(
      'Data atual formatada:',
      this.currentDate.toISOString().split('T')[0]
    );
    console.log('Range de busca:', {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    });
    console.log('Dentistas carregados:', this.dentists.length);
    console.log(
      'Estado selectedDentists antes da busca:',
      this.selectedDentists
    );

    this.schedulingService
      .getSchedulingsByDateRange(startDate, endDate)
      .subscribe({
        next: (schedulings) => {
          console.log(
            '✅ Agendamentos retornados pelo serviço:',
            schedulings.length
          );
          console.log('Agendamentos detalhados:', schedulings);
          this.schedulings = schedulings;
          this.processSchedulings();
        },
        error: (error) => {
          console.error('❌ Erro ao carregar agendamentos:', error);
          this.schedulings = [];
          this.appointments = [];
        },
      });
  }

  processSchedulings(): void {
    // Filtrar agendamentos pelos dentistas selecionados
    const selectedDentistIds = this.getSelectedDentists();

    console.log('=== DEBUG AGENDAMENTOS ===');
    console.log('Agendamentos carregados:', this.schedulings.length);
    console.log('Dentistas selecionados:', selectedDentistIds);
    console.log(
      'Todos os dentistas disponíveis:',
      this.dentists.map((d) => ({ id: d.id, name: d.name }))
    );
    console.log('Estado selectedDentists:', this.selectedDentists);
    console.log(
      'Agendamentos:',
      this.schedulings.map((s) => ({
        id: s.id,
        patientName: s.patient?.name || s.patientName || 'Sem nome',
        dentistId: s.dentistId,
        date: s.date,
        time: s.time,
      }))
    );

    // Permitir que nenhum dentista seja selecionado
    // Não forçar seleção automática

    const filteredSchedulings = this.schedulings.filter((scheduling) => {
      const currentSelectedIds = this.getSelectedDentists();
      // Se não há dentistas selecionados, mostrar todos os agendamentos
      if (currentSelectedIds.length === 0) {
        return true;
      }
      // Se o agendamento não tem dentista, mostrar sempre
      if (!scheduling.dentistId) {
        return true;
      }
      // Se o dentista do agendamento está selecionado, mostrar
      const isSelected = currentSelectedIds.includes(scheduling.dentistId);
      if (!isSelected) {
        console.log(
          `❌ Agendamento ${scheduling.id} filtrado - Dentista ${scheduling.dentistId} não selecionado`
        );
      } else {
        console.log(
          `✅ Agendamento ${scheduling.id} incluído - Dentista ${scheduling.dentistId} selecionado`
        );
      }
      return isSelected;
    });

    console.log('Agendamentos filtrados:', filteredSchedulings.length);

    // Converter agendamentos para o formato da agenda
    this.appointments = filteredSchedulings.map((scheduling) => {
      // Normalizar o horário para formato HH:MM (remover segundos se existir)
      const startTime = scheduling.time.substring(0, 5); // "09:00:00" -> "09:00"
      const duration = this.estimateDuration(scheduling);
      const endTime = this.calculateEndTime(startTime, duration);

      // Extrair nome do paciente corretamente
      const patientName =
        scheduling.patient?.name ||
        scheduling.patientName ||
        'Paciente não identificado';

      // Corrigir conversão da data - garantir que seja interpretada corretamente
      let appointmentDate: Date;
      const dateValue = scheduling.date as any; // Cast para any para evitar erro de TypeScript
      if (typeof dateValue === 'string') {
        // Se a data vem como string "2025-06-16", criar data local sem problemas de timezone
        const dateParts = dateValue.split('-');
        appointmentDate = new Date(
          parseInt(dateParts[0]),
          parseInt(dateParts[1]) - 1,
          parseInt(dateParts[2])
        );
      } else {
        appointmentDate = new Date(dateValue);
      }

      // Extrair patientId corretamente - priorizar scheduling.patientId se existir
      const patientId = scheduling.patientId || scheduling.patient?.id || 0;

      // Log para debug quando patientId for problemático
      if (!patientId || patientId === 0) {
        console.warn('⚠️ PatientId problemático no agendamento:', {
          schedulingId: scheduling.id,
          schedulingPatientId: scheduling.patientId,
          patientObjectId: scheduling.patient?.id,
          patientObject: scheduling.patient,
          patientName: patientName,
        });
      }

      const appointment = {
        id: scheduling.id,
        patientId: patientId,
        patientName: patientName,
        patientType: this.getPatientTypeFromScheduling(scheduling),
        status: scheduling.status,
        startTime: startTime,
        endTime: endTime,
        dentistId: scheduling.dentist?.id || scheduling.dentistId || 0,
        duration: duration,
        date: appointmentDate,
        notes: scheduling.notes,
        treatmentPlanId: scheduling.treatmentPlanId,
        appointmentCategory: scheduling.appointmentCategory,
      };

      // Log apenas para debug quando necessário
      if (appointment.id === 28 || appointment.id === 29) {
        console.log(`Agendamento ${appointment.id} processado:`, {
          patientName: appointment.patientName,
          dentistId: appointment.dentistId,
          date: this.formatDateForComparison(appointment.date),
          startTime: appointment.startTime,
          endTime: appointment.endTime,
        });
      }

      return appointment;
    });

    console.log('Appointments processados:', this.appointments.length);
    console.log(
      'Appointments detalhados:',
      this.appointments.map((a) => ({
        id: a.id,
        patientName: a.patientName,
        dentistId: a.dentistId,
        startTime: a.startTime,
        endTime: a.endTime,
        duration: a.duration,
        date: a.date,
        dateFormatted: this.formatDateForComparison(a.date),
        appointmentCategory: a.appointmentCategory,
      }))
    );
    console.log('Data atual:', this.currentDate);
    console.log(
      'Data atual formatada:',
      this.formatDateForComparison(this.currentDate)
    );
    console.log('Dentistas selecionados:', this.getSelectedDentists());

    // Debug específico para visualização diária
    if (this.viewMode === 'day') {
      console.log('=== DEBUG VISUALIZAÇÃO DIÁRIA ===');
      console.log('Data atual:', this.currentDate);
      console.log(
        'Data atual formatada:',
        this.formatDateForComparison(this.currentDate)
      );
      this.appointments.forEach((appointment) => {
        console.log(
          `Agendamento ${appointment.id}: ${
            appointment.patientName
          } - Data: ${this.formatDateForComparison(
            appointment.date
          )} - Dentista: ${appointment.dentistId}`
        );
      });
    }

    console.log('=== FIM DEBUG ===');

    // Se estiver na visualização mensal, distribuir agendamentos pelos dias
    if (this.viewMode === 'month') {
      this.distributeAppointmentsToMonthDays();
    }
  }

  private getPatientTypeFromScheduling(scheduling: any): string {
    // Tentar extrair o tipo do paciente dos dados disponíveis
    // A API retorna o objeto patient com patientType
    if (scheduling.patient?.patientType?.nome) {
      return scheduling.patient.patientType.nome;
    }

    // Fallback para 'default' se não houver informação específica
    return 'default';
  }

  private estimateDuration(scheduling: Scheduling): number {
    // Se o agendamento tem duração definida, usar ela
    if (scheduling.duration && scheduling.duration > 0) {
      return scheduling.duration;
    }

    // Caso contrário, usar duração padrão de 30 minutos
    return 30;
  }

  private calculateEndTime(startTime: string, duration: number): string {
    const [hours, minutes] = startTime.split(':').map(Number);
    const startMinutes = hours * 60 + minutes;
    const endMinutes = startMinutes + duration;

    const endHours = Math.floor(endMinutes / 60);
    const endMins = endMinutes % 60;

    return `${endHours.toString().padStart(2, '0')}:${endMins
      .toString()
      .padStart(2, '0')}`;
  }

  getAppointmentForSlot(
    dentistId: number,
    timeSlot: string
  ): ScheduleAppointment | null {
    // Verificar se há agendamentos para processar
    if (this.appointments.length === 0) {
      return null;
    }

    // Obter a data atual formatada para comparação
    const currentDateString = this.formatDateForComparison(this.currentDate);

    // Encontrar agendamento que INICIA neste slot específico na data atual
    const appointment =
      this.appointments.find((appointment) => {
        const dentistMatch = appointment.dentistId === dentistId;
        const timeMatch = appointment.startTime === timeSlot;
        const dateMatch =
          this.formatDateForComparison(appointment.date) === currentDateString;

        if (dentistMatch && timeMatch && !dateMatch) {
          console.log(
            `❌ Agendamento ${
              appointment.id
            } - Data não confere: ${this.formatDateForComparison(
              appointment.date
            )} vs ${currentDateString}`
          );
        }

        return dentistMatch && timeMatch && dateMatch;
      }) || null;

    return appointment;
  }

  getAppointmentHeight(appointment: ScheduleAppointment): number {
    // Altura base de cada slot: 40px em desktop/tablet, 30px em mobile
    const baseSlotHeight = this.isMobile ? 30 : 40;

    // Calcular quantos slots de 5 minutos o agendamento deve ocupar
    const startMinutes = this.timeToMinutes(appointment.startTime);
    const endMinutes = this.timeToMinutes(appointment.endTime);

    // Contar todos os slots de 5 minutos que devem ser ocupados
    // Para 09:00-10:30: slots 09:00, 09:05, 09:10, ..., 10:30 (19 slots)
    let slotsOccupied = 0;
    for (let time = startMinutes; time <= endMinutes; time += 5) {
      slotsOccupied++;
    }

    const height = slotsOccupied * baseSlotHeight;

    return height;
  }

  // Método para verificar se um slot está ocupado por um agendamento em andamento
  isSlotOccupiedByAppointment(dentistId: number, timeSlot: string): boolean {
    // Obter a data atual formatada para comparação
    const currentDateString = this.formatDateForComparison(this.currentDate);

    return this.appointments.some((appointment) => {
      if (appointment.dentistId !== dentistId) {
        return false;
      }

      // Verificar se o agendamento é da data atual
      const appointmentDateString = this.formatDateForComparison(
        appointment.date
      );
      if (appointmentDateString !== currentDateString) {
        return false;
      }

      // Converter horários para minutos para facilitar comparação
      const slotMinutes = this.timeToMinutes(timeSlot);
      const startMinutes = this.timeToMinutes(appointment.startTime);
      const endMinutes = this.timeToMinutes(appointment.endTime);

      // Verificar se o slot está dentro do período do agendamento
      // Incluir todos os slots desde o início até o slot de término (inclusive)
      // Para um agendamento de 09:00-10:30, deve ocupar slots: 09:00, 09:05, 09:10, ..., 10:30
      return slotMinutes >= startMinutes && slotMinutes <= endMinutes;
    });
  }

  // Método para obter classes CSS para slots ocupados (visualização diária)
  getSlotOccupiedClasses(dentistId: number, timeSlot: string): string {
    // Obter a data atual formatada para comparação
    const currentDateString = this.formatDateForComparison(this.currentDate);

    const appointment = this.appointments.find((appointment) => {
      if (appointment.dentistId !== dentistId) {
        return false;
      }

      // Verificar se o agendamento é da data atual
      const appointmentDateString = this.formatDateForComparison(
        appointment.date
      );
      if (appointmentDateString !== currentDateString) {
        return false;
      }

      const slotMinutes = this.timeToMinutes(timeSlot);
      const startMinutes = this.timeToMinutes(appointment.startTime);
      const endMinutes = this.timeToMinutes(appointment.endTime);

      return slotMinutes >= startMinutes && slotMinutes <= endMinutes;
    });

    if (!appointment) {
      return '';
    }

    // Obter a cor de fundo do tipo de paciente
    const patientTypeConfig = this.getPatientTypeConfig(
      appointment.patientType
    );
    const bgColorClass = patientTypeConfig.bgColor;

    // Retornar classe que remove bordas e aplica cor de fundo
    return `occupied-by-appointment ${bgColorClass}`;
  }

  // Método auxiliar para converter horário em minutos
  private timeToMinutes(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  }

  getSelectedDentistsData(): Dentist[] {
    const selectedIds = this.getSelectedDentists();
    const selectedDentists = this.dentists.filter((dentist) =>
      selectedIds.includes(dentist.id)
    );
    return selectedDentists;
  }

  getMinGridWidth(): number {
    const selectedDentistsCount = this.getSelectedDentistsData().length;

    // Ajustar larguras baseado no tamanho da tela
    let timeColumnWidth = 60;
    let minDentistColumnWidth = 120;

    if (this.isMobile) {
      timeColumnWidth = 40;
      minDentistColumnWidth = 130; // Aumentado para melhor usabilidade mobile
    } else if (this.isTablet) {
      timeColumnWidth = 50;
      minDentistColumnWidth = 100;
    }

    return timeColumnWidth + selectedDentistsCount * minDentistColumnWidth;
  }

  getMinWeekGridWidth(): number {
    const weekDaysCount = this.weekDays?.length || 6; // 6 dias (segunda a sábado)

    // Ajustar larguras baseado no tamanho da tela
    let timeColumnWidth = 60;
    let minDayColumnWidth = 140;

    if (this.isMobile) {
      timeColumnWidth = 40;
      minDayColumnWidth = 130; // Aumentado para melhor usabilidade mobile
      // Em mobile, calcular largura mínima baseada nas larguras fixas
      return timeColumnWidth + weekDaysCount * minDayColumnWidth;
    } else if (this.isTablet) {
      timeColumnWidth = 50;
      minDayColumnWidth = 120;
    }

    // Em desktop e tablet, retornar 0 para indicar que deve usar 100% da largura disponível
    // já que as colunas usam 1fr e preenchem todo o espaço
    return 0;
  }

  getDayGridColumns(): string {
    const selectedDentistsCount = this.getSelectedDentistsData().length;

    // Ajustar larguras baseado no tamanho da tela
    let timeColumnWidth = '60px';
    let minDentistColumnWidth = '120px';

    if (this.isMobile) {
      timeColumnWidth = '40px';
      // Em mobile, usar largura maior para melhor usabilidade
      minDentistColumnWidth = '130px';
    } else if (this.isTablet) {
      timeColumnWidth = '50px';
      minDentistColumnWidth = '100px';
    }

    // Em mobile, usar largura fixa ao invés de 1fr para evitar quebra de layout
    if (this.isMobile) {
      return `${timeColumnWidth} repeat(${selectedDentistsCount}, ${minDentistColumnWidth})`;
    }

    return `${timeColumnWidth} repeat(${selectedDentistsCount}, minmax(${minDentistColumnWidth}, 1fr))`;
  }

  getWeekGridColumns(): string {
    const weekDaysCount = this.weekDays?.length || 6; // 6 dias (segunda a sábado)

    // Ajustar larguras baseado no tamanho da tela
    let timeColumnWidth = '60px';
    let minDayColumnWidth = '140px';

    if (this.isMobile) {
      timeColumnWidth = '40px';
      minDayColumnWidth = '130px'; // Aumentado para melhor usabilidade mobile
      // Em mobile, usar largura fixa para garantir layout consistente
      return `${timeColumnWidth} repeat(${weekDaysCount}, ${minDayColumnWidth})`;
    } else if (this.isTablet) {
      timeColumnWidth = '50px';
      minDayColumnWidth = '120px';
    }

    // Em desktop e tablet, usar 1fr para preencher todo o espaço disponível
    // já que são apenas 6 colunas (segunda a sábado)
    return `${timeColumnWidth} repeat(${weekDaysCount}, 1fr)`;
  }

  getDentistColor(dentist: Dentist): string {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-orange-500',
      'bg-pink-500',
      'bg-indigo-500',
    ];
    return colors[dentist.id % colors.length];
  }

  getStatusBorderColor(status: string): string {
    const statusColors: { [key: string]: string } = {
      'scheduled-confirmed': 'green-500',
      'scheduled-unconfirmed': 'yellow-500',
      confirmed: 'green-500',
      unconfirmed: 'yellow-500',
      'in-progress': 'blue-500',
      completed: 'gray-500',
      cancelled: 'red-500',
      unscheduled: 'orange-500',
    };
    return statusColors[status] || 'gray-500';
  }

  getPatientTypeConfig(patientType: string): PatientType {
    return this.patientTypes[patientType] || this.patientTypes['default'];
  }

  getAppointmentCategoryColor(appointment: ScheduleAppointment): string {
    // Se há categoria de agendamento, usar sua cor
    if (appointment.appointmentCategory?.color) {
      return appointment.appointmentCategory.color;
    }

    // Fallback para cor do tipo de paciente
    const patientTypeConfig = this.getPatientTypeConfig(
      appointment.patientType
    );
    return patientTypeConfig.bgColor;
  }

  getAppointmentStatusConfig(status: string): AppointmentStatus {
    return (
      this.appointmentStatuses[status] || {
        name: 'Status desconhecido',
        color: 'text-gray-700',
        bgColor: 'bg-gray-50',
      }
    );
  }

  getDentistName(dentistId: number): string {
    const dentist = this.dentists.find((d) => d.id === dentistId);
    return dentist ? dentist.name : 'Dentista não encontrado';
  }

  // Métodos para carregar dados do agendamento
  loadAppointmentProcedures(appointmentId: number): void {
    this.schedulingService.getSchedulingProcedures(appointmentId).subscribe({
      next: (procedures: TreatmentProcedure[]) => {
        this.selectedAppointmentProcedures = procedures;
        console.log('✅ Procedimentos carregados:', procedures);

        // Atualizar o selectedAppointment com os procedureIds
        if (this.selectedAppointment) {
          this.selectedAppointment.procedureIds = procedures
            .map((p) => p.procedureId || p.id)
            .filter((id): id is number => id !== undefined);
          console.log(
            '✅ ProcedureIds atualizados no selectedAppointment:',
            this.selectedAppointment.procedureIds
          );

          // Forçar uma nova detecção de mudanças para o appointment-form
          // Isso garante que o formulário seja atualizado com os procedimentos
          setTimeout(() => {
            console.log(
              '🔄 Forçando atualização do selectedAppointment para incluir procedimentos'
            );
            this.selectedAppointment = { ...this.selectedAppointment! };
          }, 50);
        }

        // Após carregar os procedimentos, recarregar os planos de tratamento para marcar os procedimentos corretos
        if (this.selectedAppointment && this.selectedAppointment.patientId) {
          this.loadPatientTreatmentPlansForEdit(
            this.selectedAppointment.patientId,
            this.selectedAppointment.treatmentPlanId
          );
        }
      },
      error: (error) => {
        console.error(
          '❌ Erro ao carregar procedimentos do agendamento:',
          error
        );
        this.selectedAppointmentProcedures = [];

        // Mesmo em caso de erro, recarregar os planos de tratamento
        if (this.selectedAppointment && this.selectedAppointment.patientId) {
          this.loadPatientTreatmentPlansForEdit(
            this.selectedAppointment.patientId,
            this.selectedAppointment.treatmentPlanId
          );
        }
      },
    });
  }

  loadTreatmentPlan(treatmentPlanId: number): void {
    this.treatmentPlanService.getTreatmentPlan(treatmentPlanId).subscribe({
      next: (treatmentPlan: TreatmentPlan) => {
        this.selectedAppointmentTreatmentPlan = treatmentPlan;
        console.log('✅ Plano de tratamento carregado:', treatmentPlan);
      },
      error: (error) => {
        console.error('❌ Erro ao carregar plano de tratamento:', error);
        this.selectedAppointmentTreatmentPlan = null;
      },
    });
  }

  // Métodos para controle de visualização
  setViewMode(mode: ViewMode): void {
    this.viewMode = mode;
    if (mode === 'week') {
      this.generateWeekDays();
    } else if (mode === 'month') {
      this.generateMonthDays();
    }
    this.loadSchedulings();

    // Reinicializar drag scroll após mudança de visualização
    setTimeout(() => {
      this.initDragScroll();
    }, 50); // Reduzir delay para melhor responsividade
  }

  generateWeekDays(): void {
    this.weekDays = [];
    const startOfWeek = this.getStartOfWeek(this.currentDate);
    this.currentWeekStart = new Date(startOfWeek);

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    for (let i = 0; i < 6; i++) {
      // Segunda a Sábado
      const date = new Date(startOfWeek);
      date.setDate(date.getDate() + i);

      const dayNames = ['Dom', 'Seg', 'Ter', 'Qua', 'Qui', 'Sex', 'Sáb'];

      this.weekDays.push({
        date: new Date(date),
        dayName: dayNames[date.getDay()],
        dayNumber: date.getDate(),
        isToday: date.getTime() === today.getTime(),
        isWeekend: date.getDay() === 0 || date.getDay() === 6,
      });
    }
  }

  private getStartOfWeek(date: Date): Date {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1); // Ajustar para segunda-feira
    d.setDate(diff);
    d.setHours(0, 0, 0, 0);
    return d;
  }

  // Navegação de data
  goToToday(): void {
    this.currentDate = new Date();
    this.currentMonth = new Date();
    if (this.viewMode === 'week') {
      this.generateWeekDays();
    } else if (this.viewMode === 'month') {
      this.generateMonthDays();
    }
    this.loadSchedulings();
  }

  // Método para navegar para uma data específica (útil para debug)
  goToDate(dateString: string): void {
    // Criar data usando o mesmo método que usamos para os agendamentos
    const dateParts = dateString.split('-');
    const targetDate = new Date(
      parseInt(dateParts[0]),
      parseInt(dateParts[1]) - 1,
      parseInt(dateParts[2])
    );

    this.currentDate = targetDate;
    this.currentMonth = new Date(targetDate);

    console.log('Navegando para a data:', dateString);
    console.log('Data criada:', targetDate);
    console.log('Data formatada:', this.formatDateForComparison(targetDate));

    if (this.viewMode === 'week') {
      this.generateWeekDays();
    } else if (this.viewMode === 'month') {
      this.generateMonthDays();
    }
    this.loadSchedulings();
  }

  // Métodos para suportar o novo componente de formulário
  getInitialFormDate(): string {
    return this.formatDateForInput(this.currentDate);
  }

  getInitialFormTime(): string {
    // Retornar horário inicial se houver um slot selecionado
    return '';
  }

  getInitialFormDentistId(): number | undefined {
    // Para novo agendamento, sempre começar vazio
    // Para edição, retornar o dentista do agendamento sendo editado
    console.log('🔍 ScheduleV2: getInitialFormDentistId chamado');
    console.log(
      '🔍 ScheduleV2: isEditingAppointment:',
      this.isEditingAppointment
    );
    console.log(
      '🔍 ScheduleV2: selectedAppointment:',
      this.selectedAppointment
    );

    if (this.isEditingAppointment && this.selectedAppointment) {
      console.log(
        '🔍 ScheduleV2: dentistId do selectedAppointment:',
        this.selectedAppointment.dentistId
      );
      return this.selectedAppointment.dentistId;
    }
    console.log('🔍 ScheduleV2: Retornando undefined para dentistId');
    return undefined;
  }

  onAppointmentFormSubmit(formData: any): void {
    console.log('Dados do formulário recebidos:', formData);

    if (this.isEditingAppointment && this.selectedAppointment) {
      this.updateAppointment(formData);
    } else {
      this.createAppointment(formData);
    }
  }

  private createAppointment(formData: any): void {
    this.schedulingService.createScheduling(formData).subscribe({
      next: (response) => {
        console.log('Agendamento criado com sucesso:', response);
        this.notificationService.success('Agendamento criado com sucesso!');
        this.closeSheet();
        this.loadSchedulings();
      },
      error: (error) => {
        console.error('Erro ao criar agendamento:', error);
        this.notificationService.error(
          'Erro ao criar agendamento. Tente novamente.'
        );
      },
    });
  }

  private updateAppointment(formData: any): void {
    if (!this.selectedAppointment) return;

    this.schedulingService
      .updateScheduling(this.selectedAppointment.id, formData)
      .subscribe({
        next: (response) => {
          console.log('Agendamento atualizado com sucesso:', response);
          this.notificationService.success(
            'Agendamento atualizado com sucesso!'
          );
          this.closeSheet();
          this.loadSchedulings();
        },
        error: (error) => {
          console.error('Erro ao atualizar agendamento:', error);
          this.notificationService.error(
            'Erro ao atualizar agendamento. Tente novamente.'
          );
        },
      });
  }

  onFormValidityChange(isValid: boolean): void {
    console.log('🔍 ScheduleV2: onFormValidityChange chamado:', {
      isValid,
      previousValue: this.isAppointmentFormValid,
    });
    this.isAppointmentFormValid = isValid;
  }

  submitAppointmentForm(): void {
    // Trigger form submission by emitting a custom event
    // O componente filho deve escutar este evento e submeter o formulário
    const formElement = document.querySelector(
      'app-appointment-form form'
    ) as HTMLFormElement;
    if (formElement) {
      formElement.dispatchEvent(new Event('submit', { cancelable: true }));
    }
  }

  goToPreviousPeriod(): void {
    if (this.viewMode === 'week') {
      const newDate = new Date(this.currentDate);
      newDate.setDate(newDate.getDate() - 7);
      this.currentDate = newDate;
      this.generateWeekDays();
    } else if (this.viewMode === 'month') {
      const newMonth = new Date(this.currentMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      this.currentMonth = newMonth;
      this.generateMonthDays();
    } else {
      const newDate = new Date(this.currentDate);
      newDate.setDate(newDate.getDate() - 1);
      this.currentDate = newDate;
    }
    console.log('Navegando para período anterior:', this.currentDate);
    this.loadSchedulings();
  }

  goToNextPeriod(): void {
    if (this.viewMode === 'week') {
      const newDate = new Date(this.currentDate);
      newDate.setDate(newDate.getDate() + 7);
      this.currentDate = newDate;
      this.generateWeekDays();
    } else if (this.viewMode === 'month') {
      const newMonth = new Date(this.currentMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      this.currentMonth = newMonth;
      this.generateMonthDays();
    } else {
      const newDate = new Date(this.currentDate);
      newDate.setDate(newDate.getDate() + 1);
      this.currentDate = newDate;
    }
    console.log('Navegando para próximo período:', this.currentDate);
    this.loadSchedulings();
  }

  // Métodos para visualização semanal
  getAppointmentForWeekSlot(
    date: Date,
    timeSlot: string
  ): ScheduleAppointment | null {
    const dateString = this.formatDateForComparison(date);
    const selectedDentistIds = this.getSelectedDentists();

    return (
      this.appointments.find((appointment) => {
        const appointmentDateString = this.formatDateForComparison(
          appointment.date
        );
        const dateMatch = appointmentDateString === dateString;
        const timeMatch = appointment.startTime === timeSlot;

        // Se nenhum dentista está selecionado, mostrar todos os agendamentos
        const dentistMatch =
          selectedDentistIds.length === 0 ||
          selectedDentistIds.includes(appointment.dentistId);

        return dateMatch && timeMatch && dentistMatch;
      }) || null
    );
  }

  // Novo método para obter todos os agendamentos que começam em um slot específico
  getAppointmentsForWeekSlot(
    date: Date,
    timeSlot: string
  ): ScheduleAppointment[] {
    const dateString = this.formatDateForComparison(date);
    const selectedDentistIds = this.getSelectedDentists();

    return this.appointments.filter((appointment) => {
      const appointmentDateString = this.formatDateForComparison(
        appointment.date
      );
      const dateMatch = appointmentDateString === dateString;
      const timeMatch = appointment.startTime === timeSlot;

      // Se nenhum dentista está selecionado, mostrar todos os agendamentos
      const dentistMatch =
        selectedDentistIds.length === 0 ||
        selectedDentistIds.includes(appointment.dentistId);

      return dateMatch && timeMatch && dentistMatch;
    });
  }

  // Método específico para obter agendamentos que começam em um slot (mesmo que o anterior, mas com nome mais claro)
  getAppointmentsStartingInWeekSlot(
    date: Date,
    timeSlot: string
  ): ScheduleAppointment[] {
    return this.getAppointmentsForWeekSlot(date, timeSlot);
  }

  // Método para calcular a posição esquerda de um agendamento na visualização semanal (em porcentagem)
  getWeekAppointmentLeftPosition(
    index: number,
    totalAppointments: number
  ): number {
    if (totalAppointments === 1) {
      return 2; // Margem padrão quando há apenas um agendamento
    }

    // Calcular largura disponível (considerando margens de 2px de cada lado = 4px total)
    // Usar porcentagem da largura disponível
    const marginPercent = 1; // 1% de margem de cada lado
    const availablePercent = 100 - marginPercent * 2;
    const appointmentWidthPercent = availablePercent / totalAppointments;

    return marginPercent + index * appointmentWidthPercent;
  }

  // Método para calcular a posição direita de um agendamento na visualização semanal (em porcentagem)
  getWeekAppointmentRightPosition(
    index: number,
    totalAppointments: number
  ): number {
    if (totalAppointments === 1) {
      return 2; // Margem padrão quando há apenas um agendamento
    }

    // Calcular largura disponível (considerando margens de 2px de cada lado = 4px total)
    // Usar porcentagem da largura disponível
    const marginPercent = 1; // 1% de margem de cada lado
    const availablePercent = 100 - marginPercent * 2;
    const appointmentWidthPercent = availablePercent / totalAppointments;

    // Calcular posição direita baseada no índice
    const remainingAppointments = totalAppointments - index - 1;
    return marginPercent + remainingAppointments * appointmentWidthPercent;
  }

  // Método para verificar se um slot está ocupado na visualização semanal
  isWeekSlotOccupiedByAppointment(date: Date, timeSlot: string): boolean {
    const dateString = this.formatDateForComparison(date);
    return this.appointments.some((appointment) => {
      const appointmentDateString = this.formatDateForComparison(
        appointment.date
      );
      if (appointmentDateString !== dateString) {
        return false;
      }

      // Converter horários para minutos para facilitar comparação
      const slotMinutes = this.timeToMinutes(timeSlot);
      const startMinutes = this.timeToMinutes(appointment.startTime);
      const endMinutes = this.timeToMinutes(appointment.endTime);

      // Verificar se o slot está dentro do período do agendamento
      // Incluir todos os slots desde o início até o slot de término (inclusive)
      return slotMinutes >= startMinutes && slotMinutes <= endMinutes;
    });
  }

  // Método para obter classes CSS para slots ocupados (visualização semanal)
  getWeekSlotOccupiedClasses(date: Date, timeSlot: string): string {
    const dateString = this.formatDateForComparison(date);
    const appointment = this.appointments.find((appointment) => {
      const appointmentDateString = this.formatDateForComparison(
        appointment.date
      );
      if (appointmentDateString !== dateString) {
        return false;
      }

      const slotMinutes = this.timeToMinutes(timeSlot);
      const startMinutes = this.timeToMinutes(appointment.startTime);
      const endMinutes = this.timeToMinutes(appointment.endTime);

      return slotMinutes >= startMinutes && slotMinutes <= endMinutes;
    });

    if (!appointment) {
      return '';
    }

    // Obter a cor de fundo do tipo de paciente
    const patientTypeConfig = this.getPatientTypeConfig(
      appointment.patientType
    );
    const bgColorClass = patientTypeConfig.bgColor;

    // Retornar classe que remove bordas e aplica cor de fundo
    return `occupied-by-appointment ${bgColorClass}`;
  }

  private formatDateForComparison(date: Date): string {
    // Usar getFullYear, getMonth e getDate para evitar problemas de timezone
    // que podem ocorrer com toISOString()
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  getCurrentPeriodLabel(): string {
    if (this.viewMode === 'week') {
      const startDate = this.weekDays[0]?.date;
      const endDate = this.weekDays[this.weekDays.length - 1]?.date;

      if (startDate && endDate) {
        const startMonth = startDate.toLocaleDateString('pt-BR', {
          month: 'short',
        });
        const endMonth = endDate.toLocaleDateString('pt-BR', {
          month: 'short',
        });

        if (startDate.getMonth() === endDate.getMonth()) {
          return `${startDate.getDate()} - ${endDate.getDate()} de ${startMonth} ${startDate.getFullYear()}`;
        } else {
          return `${startDate.getDate()} ${startMonth} - ${endDate.getDate()} ${endMonth} ${startDate.getFullYear()}`;
        }
      }
    } else if (this.viewMode === 'month') {
      return `${
        this.monthNames[this.currentMonth.getMonth()]
      } ${this.currentMonth.getFullYear()}`;
    }

    return this.currentDate.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }

  // Métodos para visualização mensal
  generateMonthDays(): void {
    this.monthDays = [];
    const year = this.currentMonth.getFullYear();
    const month = this.currentMonth.getMonth();

    // Primeiro dia do mês
    const firstDay = new Date(year, month, 1);
    // Último dia do mês
    const lastDay = new Date(year, month + 1, 0);

    // Primeiro dia da semana (domingo = 0)
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    // Último dia da semana
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - lastDay.getDay()));

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Gerar todos os dias do calendário (incluindo dias dos meses anterior e posterior)
    const currentDate = new Date(startDate);
    while (currentDate <= endDate) {
      this.monthDays.push({
        date: new Date(currentDate),
        dayNumber: currentDate.getDate(),
        isToday: currentDate.getTime() === today.getTime(),
        isCurrentMonth: currentDate.getMonth() === month,
        isWeekend: currentDate.getDay() === 0 || currentDate.getDay() === 6,
        appointments: [],
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }
  }

  distributeAppointmentsToMonthDays(): void {
    // Limpar agendamentos existentes
    this.monthDays.forEach((day) => (day.appointments = []));

    // Distribuir agendamentos pelos dias
    this.appointments.forEach((appointment) => {
      const appointmentDate = this.formatDateForComparison(appointment.date);

      const monthDay = this.monthDays.find(
        (day) => this.formatDateForComparison(day.date) === appointmentDate
      );

      if (monthDay) {
        monthDay.appointments.push(appointment);
      }
    });

    // Ordenar agendamentos por horário em cada dia
    this.monthDays.forEach((day) => {
      day.appointments.sort((a, b) => a.startTime.localeCompare(b.startTime));
    });
  }

  getMonthWeeks(): MonthDay[][] {
    const weeks: MonthDay[][] = [];
    for (let i = 0; i < this.monthDays.length; i += 7) {
      weeks.push(this.monthDays.slice(i, i + 7));
    }
    return weeks;
  }

  getVisibleAppointments(day: MonthDay): ScheduleAppointment[] {
    // Mostrar no máximo 3 agendamentos por dia
    return day.appointments.slice(0, 3);
  }

  getHiddenAppointmentsCount(day: MonthDay): number {
    return Math.max(0, day.appointments.length - 3);
  }

  getAppointmentTooltip(appointment: ScheduleAppointment): string {
    const patientType = this.getPatientTypeConfig(appointment.patientType);
    const status = this.getAppointmentStatusConfig(appointment.status);

    return (
      `👤 ${appointment.patientName}\n` +
      `🕒 ${appointment.startTime} - ${appointment.endTime} (${appointment.duration} min)\n` +
      `📋 Tipo: ${patientType.name}\n` +
      `📊 Status: ${status.name}` +
      (appointment.notes ? `\n📝 Observações: ${appointment.notes}` : '') +
      `\n\n💡 Clique para editar ou visualizar detalhes`
    );
  }

  private saveSelectedDentistsToStorage(): void {
    try {
      const selectionToSave = { ...this.selectedDentists };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(selectionToSave));
    } catch (error) {
      console.warn(
        'Erro ao salvar seleção de dentistas no localStorage:',
        error
      );
    }
  }

  private loadSelectedDentistsFromStorage(): { [key: string]: boolean } | null {
    try {
      const saved = localStorage.getItem(this.STORAGE_KEY);
      if (saved) {
        return JSON.parse(saved);
      }
    } catch (error) {
      console.warn(
        'Erro ao carregar seleção de dentistas do localStorage:',
        error
      );
    }
    return null;
  }

  // Método para limpar a seleção salva (útil para debugging ou reset)
  clearSavedSelection(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
      console.log('Seleção de dentistas limpa do localStorage');
      // Reinicializar todos como desmarcados
      this.dentists.forEach((dentist) => {
        this.selectedDentists[dentist.id] = false;
      });
      this.loadSchedulings();
    } catch (error) {
      console.warn(
        'Erro ao limpar seleção de dentistas do localStorage:',
        error
      );
    }
  }

  // Métodos para controle do filtro dropdown
  toggleFilterDropdown(): void {
    this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
  }

  closeFilterDropdown(): void {
    this.isFilterDropdownOpen = false;
  }

  onDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (
      !target.closest('.filter-dropdown-container') &&
      this.isFilterDropdownOpen
    ) {
      this.isFilterDropdownOpen = false;
    }
  }

  // Getter para determinar se deve mostrar o filtro inline ou como dropdown
  get shouldShowFilterInline(): boolean {
    return this.isSidebarCollapsed;
  }

  get shouldShowFilterButton(): boolean {
    return !this.isSidebarCollapsed;
  }

  // Métodos de responsividade
  private checkScreenSize(): void {
    const width = window.innerWidth;
    this.isMobile = width <= 640;
    this.isTablet = width > 640 && width <= 1024;
    this.isDesktop = width > 1024;

    // Reinicializar drag scroll quando o tamanho da tela mudar
    setTimeout(() => {
      this.initDragScroll();
    }, 50); // Reduzir delay para melhor responsividade
  }

  private onWindowResize(): void {
    this.checkScreenSize();
    // Fechar dropdown em mobile se estiver aberto
    if (this.isMobile && this.isFilterDropdownOpen) {
      this.isFilterDropdownOpen = false;
    }
  }

  // Getter para determinar se deve mostrar filtros inline baseado na responsividade
  get shouldShowFilterInlineResponsive(): boolean {
    // Em mobile, sempre usar dropdown, mesmo com sidebar colapsado
    if (this.isMobile) {
      return false;
    }
    return this.shouldShowFilterInline;
  }

  // Getter para determinar se deve mostrar botão de filtros baseado na responsividade
  get shouldShowFilterButtonResponsive(): boolean {
    // Em mobile, sempre mostrar botão de filtros
    if (this.isMobile) {
      return true;
    }
    return this.shouldShowFilterButton;
  }

  // Métodos para controle do histórico de observações
  openObservationsHistory(): void {
    const patientId = this.appointmentForm.get('patientId')?.value;
    if (patientId) {
      this.selectedPatientIdForHistory = patientId;
      this.isObservationsHistoryOpen = true;
    }
  }

  openObservationsHistoryFromDetails(): void {
    const patientId = this.selectedAppointment?.patientId;
    if (patientId) {
      this.selectedPatientIdForHistory = patientId;
      this.isObservationsHistoryOpen = true;
    }
  }

  closeObservationsHistory(): void {
    this.isObservationsHistoryOpen = false;
    this.selectedPatientIdForHistory = 0;
  }

  // Método chamado quando um horário é selecionado no componente de horários disponíveis
  onTimeSelected(time: string): void {
    this.appointmentForm.patchValue({ time });
    this.calculateDurationAndEndTime();
  }
}

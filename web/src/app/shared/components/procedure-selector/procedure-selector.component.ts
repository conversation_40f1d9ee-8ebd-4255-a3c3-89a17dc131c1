import {
  Component,
  Input,
  Output,
  EventEmitter,
  OnInit,
  forwardRef,
} from '@angular/core';
import { CommonModule } from '@angular/common';
import {
  FormsModule,
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
} from '@angular/forms';
import { ProcedureService } from '../../../core/services/procedure.service';
import { Procedure } from '../../../core/models/procedure.model';

@Component({
  selector: 'app-procedure-selector',
  templateUrl: './procedure-selector.component.html',
  styleUrls: ['./procedure-selector.component.scss'],
  standalone: true,
  imports: [CommonModule, FormsModule],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ProcedureSelectorComponent),
      multi: true,
    },
  ],
})
export class ProcedureSelectorComponent
  implements OnInit, ControlValueAccessor
{
  @Input() placeholder: string = 'Pesquisar procedimentos...';
  @Input() disabled: boolean = false;
  @Output() proceduresChange = new EventEmitter<Procedure[]>();

  searchTerm: string = '';
  allProcedures: Procedure[] = [];
  filteredProcedures: Procedure[] = [];
  selectedProcedures: Procedure[] = [];
  showDropdown: boolean = false;
  isLoading: boolean = false;
  private pendingValue?: number[];

  // ControlValueAccessor
  private onChange = (value: any) => {};
  private onTouched = () => {};

  constructor(private procedureService: ProcedureService) {}

  ngOnInit(): void {
    this.loadProcedures();
  }

  loadProcedures(): void {
    this.isLoading = true;
    this.procedureService.getAllProcedures().subscribe({
      next: (procedures) => {
        this.allProcedures = procedures.filter((p) => p.status === 'ACTIVE');
        this.isLoading = false;

        console.log(
          '✅ ProcedureSelector: Procedimentos carregados:',
          this.allProcedures.map((p) => ({ id: p.id, name: p.name }))
        );

        // Processar valor pendente se existir
        if (this.pendingValue && this.pendingValue.length > 0) {
          console.log(
            '🔄 ProcedureSelector: Processando valor pendente:',
            this.pendingValue
          );
          this.selectedProcedures = this.allProcedures.filter((p) =>
            this.pendingValue!.includes(p.id)
          );
          console.log(
            '✅ ProcedureSelector: Procedimentos selecionados após carregamento:',
            this.selectedProcedures.map((p) => ({ id: p.id, name: p.name }))
          );

          // Emitir mudança para notificar o formulário pai
          if (this.selectedProcedures.length > 0) {
            this.emitChange();
          }

          this.pendingValue = undefined;
        }
      },
      error: (error) => {
        console.error('Erro ao carregar procedimentos:', error);
        this.isLoading = false;
      },
    });
  }

  onSearchInput(): void {
    if (!this.searchTerm.trim()) {
      this.filteredProcedures = [];
      this.showDropdown = false;
      return;
    }

    const searchLower = this.searchTerm.toLowerCase();
    this.filteredProcedures = this.allProcedures
      .filter(
        (procedure) =>
          !this.isProcedureSelected(procedure) &&
          (procedure.name.toLowerCase().includes(searchLower) ||
            procedure.description?.toLowerCase().includes(searchLower) ||
            procedure.type.toLowerCase().includes(searchLower))
      )
      .slice(0, 10); // Limitar a 10 resultados

    this.showDropdown = this.filteredProcedures.length > 0;
  }

  onSearchFocus(): void {
    if (this.searchTerm.trim()) {
      this.onSearchInput();
    }
  }

  onSearchBlur(): void {
    // Delay para permitir clique nos itens do dropdown
    setTimeout(() => {
      this.showDropdown = false;
    }, 200);
  }

  selectProcedure(procedure: Procedure): void {
    if (!this.isProcedureSelected(procedure)) {
      this.selectedProcedures.push(procedure);
      this.searchTerm = '';
      this.showDropdown = false;
      this.filteredProcedures = [];
      this.emitChange();
    }
  }

  removeProcedure(procedure: Procedure): void {
    const index = this.selectedProcedures.findIndex(
      (p) => p.id === procedure.id
    );
    if (index > -1) {
      this.selectedProcedures.splice(index, 1);
      this.emitChange();
    }
  }

  isProcedureSelected(procedure: Procedure): boolean {
    return this.selectedProcedures.some((p) => p.id === procedure.id);
  }

  getTotalDuration(): number {
    return this.selectedProcedures.reduce(
      (total, p) => total + p.estimatedDuration,
      0
    );
  }

  getTotalPrice(): number {
    return this.selectedProcedures.reduce(
      (total, p) => total + p.defaultPrice,
      0
    );
  }

  private emitChange(): void {
    this.proceduresChange.emit([...this.selectedProcedures]);
    this.onChange(this.selectedProcedures.map((p) => p.id));
    this.onTouched();
  }

  // ControlValueAccessor implementation
  writeValue(value: number[]): void {
    console.log('🔍 ProcedureSelector: writeValue chamado:', {
      value,
      allProceduresLength: this.allProcedures.length,
      allProcedures: this.allProcedures.map((p) => ({
        id: p.id,
        name: p.name,
      })),
    });

    if (value && Array.isArray(value) && value.length > 0) {
      if (this.allProcedures.length > 0) {
        this.selectedProcedures = this.allProcedures.filter((p) =>
          value.includes(p.id)
        );
        console.log(
          '✅ ProcedureSelector: Procedimentos selecionados:',
          this.selectedProcedures.map((p) => ({ id: p.id, name: p.name }))
        );

        // Limpar valor pendente se foi processado com sucesso
        this.pendingValue = undefined;
      } else {
        // Se os procedimentos ainda não foram carregados, aguardar
        console.log(
          '⏳ ProcedureSelector: Aguardando carregamento dos procedimentos...'
        );
        this.pendingValue = value;

        // Tentar carregar procedimentos se ainda não foram carregados
        if (!this.isLoading) {
          console.log(
            '🔄 ProcedureSelector: Tentando carregar procedimentos...'
          );
          this.loadProcedures();
        }
      }
    } else {
      this.selectedProcedures = [];
      this.pendingValue = undefined;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  getTypeLabel(type: string): string {
    const typeLabels: { [key: string]: string } = {
      CLINICAL: 'Clínico',
      SURGICAL: 'Cirúrgico',
      AESTHETIC: 'Estético',
      ORTHODONTIC: 'Ortodôntico',
      ENDODONTIC: 'Endodôntico',
      PERIODONTIC: 'Periodontal',
      PEDIATRIC: 'Pediátrico',
      RADIOLOGY: 'Radiologia',
      PROSTHODONTIC: 'Protético',
    };
    return typeLabels[type] || type;
  }

  getTypeColor(type: string): string {
    const typeColors: { [key: string]: string } = {
      CLINICAL: 'bg-blue-100 text-blue-800',
      SURGICAL: 'bg-red-100 text-red-800',
      AESTHETIC: 'bg-pink-100 text-pink-800',
      ORTHODONTIC: 'bg-purple-100 text-purple-800',
      ENDODONTIC: 'bg-orange-100 text-orange-800',
      PERIODONTIC: 'bg-green-100 text-green-800',
      PEDIATRIC: 'bg-yellow-100 text-yellow-800',
      RADIOLOGY: 'bg-gray-100 text-gray-800',
      PROSTHODONTIC: 'bg-indigo-100 text-indigo-800',
    };
    return typeColors[type] || 'bg-gray-100 text-gray-800';
  }
}
